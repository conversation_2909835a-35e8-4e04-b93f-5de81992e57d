<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Space Fighter Test</title>

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🚀</text></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🚀</text></svg>">

    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        
        .test-container {
            text-align: center;
            background: #111;
            padding: 40px;
            border-radius: 10px;
            border: 2px solid #00d4ff;
        }
        
        .test-btn {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.2rem;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: #e55a2b;
            transform: scale(1.05);
        }
        
        #gameArea {
            width: 400px;
            height: 300px;
            background: #000;
            border: 2px solid #00d4ff;
            margin: 20px auto;
            position: relative;
            display: none;
        }
        
        #player {
            width: 30px;
            height: 30px;
            background: #00d4ff;
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .status {
            margin: 20px 0;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Space Fighter Test</h1>
        <p>Testing basic functionality...</p>
        
        <div class="status" id="status">Ready to test</div>
        
        <button class="test-btn" onclick="testStart()">Test Start Game</button>
        <button class="test-btn" onclick="testCanvas()">Test Canvas</button>
        <button class="test-btn" onclick="goToMainGame()">Go to Main Game</button>
        
        <div id="gameArea">
            <div id="player"></div>
        </div>
        
        <canvas id="testCanvas" width="400" height="300" style="border: 2px solid #00d4ff; display: none;"></canvas>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
            console.log('Status:', message);
        }
        
        function testStart() {
            updateStatus('Testing start functionality...');
            
            const gameArea = document.getElementById('gameArea');
            gameArea.style.display = 'block';
            
            updateStatus('✅ Start test successful!');
        }
        
        function testCanvas() {
            updateStatus('Testing canvas...');
            
            const canvas = document.getElementById('testCanvas');
            const ctx = canvas.getContext('2d');
            
            if (!ctx) {
                updateStatus('❌ Canvas context failed!');
                return;
            }
            
            canvas.style.display = 'block';
            
            // Draw test graphics
            ctx.fillStyle = '#ff6b35';
            ctx.fillRect(50, 50, 100, 100);
            
            ctx.fillStyle = '#00d4ff';
            ctx.fillRect(200, 100, 50, 50);
            
            ctx.fillStyle = '#fff';
            ctx.font = '20px Arial';
            ctx.fillText('Canvas Works!', 120, 200);
            
            updateStatus('✅ Canvas test successful!');
        }
        
        function goToMainGame() {
            window.location.href = 'index.html';
        }
        
        // Test on load
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('✅ Page loaded successfully');
            console.log('Test page ready');
        });
    </script>
</body>
</html>
