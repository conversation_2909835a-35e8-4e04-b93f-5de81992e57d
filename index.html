<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Space Fighter - Defend the Galaxy!</title>
    <meta name="description" content="An exciting space shooter game. Defend the galaxy from alien invaders!">

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🚀</text></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🚀</text></svg>">

    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Game Container -->
    <div id="gameContainer">
        <!-- Start Screen -->
        <div id="startScreen" class="screen active">
            <div class="title-container">
                <div class="logo-title">
                    <div class="space-logo">🚀</div>
                    <h1 class="game-title">SPACE FIGHTER</h1>
                    <div class="space-logo">👾</div>
                </div>
                <p class="game-subtitle">Defend the Galaxy from Alien Invaders!</p>
            </div>
            
            <div class="menu-container">
                <button id="startBtn" class="game-btn primary">
                    <span>🚀 START GAME</span>
                </button>
                <button id="instructionsBtn" class="game-btn secondary">
                    <span>📖 HOW TO PLAY</span>
                </button>
                <button id="highScoreBtn" class="game-btn secondary">
                    <span>🏆 HIGH SCORES</span>
                </button>
            </div>
            
            <div class="stars-bg"></div>
        </div>

        <!-- Instructions Screen -->
        <div id="instructionsScreen" class="screen">
            <div class="instructions-container">
                <div id="welcomeMessage" class="welcome-message" style="display: none;">
                    <h2>🎉 Welcome to Space Fighter!</h2>
                    <p>Let's learn how to defend the galaxy!</p>
                </div>
                <h2 id="instructionsTitle">How to Play</h2>
                <div class="instructions-content">
                    <div class="instruction-item">
                        <span class="key">←→</span>
                        <span class="description">Move left and right</span>
                    </div>
                    <div class="instruction-item">
                        <span class="key">SPACE</span>
                        <span class="description">Shoot lasers</span>
                    </div>
                    <div class="instruction-item">
                        <span class="key">P</span>
                        <span class="description">Pause game</span>
                    </div>
                </div>
                
                <div class="game-rules">
                    <h3>Game Rules:</h3>
                    <ul>
                        <li>🎯 Destroy alien ships to earn points</li>
                        <li>💥 Avoid enemy bullets and collisions</li>
                        <li>❤️ You have 3 lives to start</li>
                        <li>⚡ Collect power-ups for special abilities</li>
                        <li>🏆 Beat your high score!</li>
                    </ul>
                </div>
                
                <div class="instruction-buttons">
                    <button id="backBtn" class="game-btn secondary">
                        <span>← BACK TO MENU</span>
                    </button>
                    <button id="startPlayingBtn" class="game-btn primary" style="display: none;">
                        <span>🎮 GOT IT, LET'S PLAY!</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- High Scores Screen -->
        <div id="highScoreScreen" class="screen">
            <div class="highscore-container">
                <h2>🏆 High Scores</h2>
                <div id="highScoreList" class="score-list">
                    <!-- High scores will be populated by JavaScript -->
                </div>
                <button id="clearScoresBtn" class="game-btn secondary">
                    <span>🗑️ CLEAR SCORES</span>
                </button>
                <button id="backFromScoresBtn" class="game-btn primary">
                    <span>← BACK TO MENU</span>
                </button>
            </div>
        </div>

        <!-- Game Screen -->
        <div id="gameScreen" class="screen">
            <div class="game-ui">
                <div class="ui-left">
                    <div class="score">Score: <span id="score">0</span></div>
                    <div class="level">Level: <span id="level">1</span></div>
                </div>
                <div class="ui-center">
                    <button id="pauseBtn" class="pause-btn">⏸️</button>
                </div>
                <div class="ui-right">
                    <div class="lives">Lives: <span id="lives">3</span></div>
                    <div class="power">Power: <span id="power">Normal</span></div>
                </div>
            </div>
            
            <canvas id="gameCanvas" width="800" height="600"></canvas>
            
            <div class="mobile-controls">
                <button id="moveLeftBtn" class="mobile-btn">←</button>
                <button id="shootBtn" class="mobile-btn shoot">🔥</button>
                <button id="moveRightBtn" class="mobile-btn">→</button>
            </div>
        </div>

        <!-- Pause Screen -->
        <div id="pauseScreen" class="screen overlay">
            <div class="pause-container">
                <h2>⏸️ GAME PAUSED</h2>
                <div class="pause-menu">
                    <button id="resumeBtn" class="game-btn primary">
                        <span>▶️ RESUME</span>
                    </button>
                    <button id="restartBtn" class="game-btn secondary">
                        <span>🔄 RESTART</span>
                    </button>
                    <button id="quitBtn" class="game-btn secondary">
                        <span>🏠 MAIN MENU</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Game Over Screen -->
        <div id="gameOverScreen" class="screen overlay">
            <div class="gameover-container">
                <h2 class="gameover-title">GAME OVER</h2>
                <div class="final-stats">
                    <div class="stat">
                        <span class="stat-label">Final Score:</span>
                        <span class="stat-value" id="finalScore">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Level Reached:</span>
                        <span class="stat-value" id="finalLevel">1</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Enemies Destroyed:</span>
                        <span class="stat-value" id="enemiesDestroyed">0</span>
                    </div>
                </div>
                
                <div id="newHighScore" class="new-high-score" style="display: none;">
                    🎉 NEW HIGH SCORE! 🎉
                </div>
                
                <div class="name-input" id="nameInput" style="display: none;">
                    <input type="text" id="playerName" placeholder="Enter your name" maxlength="10">
                    <button id="saveScoreBtn" class="game-btn primary">Save Score</button>
                </div>
                
                <div class="gameover-menu">
                    <button id="playAgainBtn" class="game-btn primary">
                        <span>🔄 PLAY AGAIN</span>
                    </button>
                    <button id="mainMenuBtn" class="game-btn secondary">
                        <span>🏠 MAIN MENU</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>
