<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Space Fighter</title>

    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🚀</text></svg>">
    <link rel="apple-touch-icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🚀</text></svg>">

    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        
        h1 {
            color: #00d4ff;
            text-shadow: 0 0 20px #00d4ff;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .logo {
            font-size: 2rem;
            animation: spin 4s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.2rem;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: #e55a2b;
            transform: scale(1.05);
        }
        
        #gameCanvas {
            border: 2px solid #00d4ff;
            background: #000;
            margin: 20px;
        }
        
        .controls {
            margin: 20px;
            text-align: center;
        }
        
        .score {
            font-size: 1.5rem;
            color: #00d4ff;
            margin: 10px;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div id="menu">
        <h1><span class="logo">🚀</span> SPACE FIGHTER <span class="logo">👾</span></h1>
        <p>Simple version - guaranteed to work!</p>
        <button onclick="startSimpleGame()">START GAME</button>
        <button onclick="window.location.href='index.html'">Full Version</button>
    </div>
    
    <div id="game" class="hidden">
        <div class="score">Score: <span id="score">0</span> | Lives: <span id="lives">3</span></div>
        <canvas id="gameCanvas" width="600" height="400"></canvas>
        <div class="controls">
            <p>Use ← → arrow keys to move, SPACE to shoot</p>
            <button onclick="pauseSimpleGame()">PAUSE</button>
            <button onclick="endGame()">END GAME</button>
        </div>
    </div>
    
    <div id="gameOver" class="hidden">
        <h2>GAME OVER</h2>
        <p>Final Score: <span id="finalScore">0</span></p>
        <button onclick="startSimpleGame()">PLAY AGAIN</button>
        <button onclick="showMenu()">MAIN MENU</button>
    </div>

    <script>
        // Simple game variables
        let canvas, ctx;
        let player = { x: 275, y: 350, width: 50, height: 30 };
        let bullets = [];
        let enemies = [];
        let score = 0;
        let lives = 3;
        let gameRunning = false;
        let keys = {};
        
        function startSimpleGame() {
            console.log('Starting simple game...');
            
            // Hide menu, show game
            document.getElementById('menu').classList.add('hidden');
            document.getElementById('gameOver').classList.add('hidden');
            document.getElementById('game').classList.remove('hidden');
            
            // Initialize canvas
            canvas = document.getElementById('gameCanvas');
            ctx = canvas.getContext('2d');
            
            // Reset game state
            score = 0;
            lives = 3;
            bullets = [];
            enemies = [];
            player.x = 275;
            player.y = 350;
            
            updateSimpleUI();
            
            gameRunning = true;
            gameLoop();
            
            console.log('Simple game started!');
        }
        
        function gameLoop() {
            if (!gameRunning) return;
            
            // Clear canvas
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Update and draw player
            if (keys['ArrowLeft'] && player.x > 0) player.x -= 5;
            if (keys['ArrowRight'] && player.x < canvas.width - player.width) player.x += 5;
            
            // Draw player
            ctx.fillStyle = '#00d4ff';
            ctx.fillRect(player.x, player.y, player.width, player.height);
            
            // Update bullets
            bullets.forEach((bullet, index) => {
                bullet.y -= 8;
                if (bullet.y < 0) bullets.splice(index, 1);
            });
            
            // Draw bullets
            ctx.fillStyle = '#fff';
            bullets.forEach(bullet => {
                ctx.fillRect(bullet.x, bullet.y, 4, 10);
            });
            
            // Spawn enemies
            if (Math.random() < 0.02) {
                enemies.push({
                    x: Math.random() * (canvas.width - 30),
                    y: -30,
                    width: 30,
                    height: 30
                });
            }
            
            // Update enemies
            enemies.forEach((enemy, index) => {
                enemy.y += 2;
                if (enemy.y > canvas.height) enemies.splice(index, 1);
            });
            
            // Draw enemies
            ctx.fillStyle = '#ff6b35';
            enemies.forEach(enemy => {
                ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
            });
            
            // Check collisions
            bullets.forEach((bullet, bIndex) => {
                enemies.forEach((enemy, eIndex) => {
                    if (bullet.x < enemy.x + enemy.width &&
                        bullet.x + 4 > enemy.x &&
                        bullet.y < enemy.y + enemy.height &&
                        bullet.y + 10 > enemy.y) {
                        bullets.splice(bIndex, 1);
                        enemies.splice(eIndex, 1);
                        score += 10;
                        updateSimpleUI();
                    }
                });
            });
            
            // Check enemy collisions with player
            enemies.forEach((enemy, index) => {
                if (enemy.x < player.x + player.width &&
                    enemy.x + enemy.width > player.x &&
                    enemy.y < player.y + player.height &&
                    enemy.y + enemy.height > player.y) {
                    enemies.splice(index, 1);
                    lives--;
                    updateSimpleUI();
                    
                    if (lives <= 0) {
                        endGame();
                    }
                }
            });
            
            requestAnimationFrame(gameLoop);
        }
        
        function updateSimpleUI() {
            document.getElementById('score').textContent = score;
            document.getElementById('lives').textContent = lives;
        }
        
        function pauseSimpleGame() {
            gameRunning = !gameRunning;
            if (gameRunning) gameLoop();
        }
        
        function endGame() {
            gameRunning = false;
            document.getElementById('game').classList.add('hidden');
            document.getElementById('gameOver').classList.remove('hidden');
            document.getElementById('finalScore').textContent = score;
        }
        
        function showMenu() {
            document.getElementById('gameOver').classList.add('hidden');
            document.getElementById('menu').classList.remove('hidden');
        }
        
        // Keyboard controls
        document.addEventListener('keydown', function(e) {
            keys[e.key] = true;
            
            if (e.key === ' ' && gameRunning) {
                bullets.push({
                    x: player.x + player.width/2 - 2,
                    y: player.y
                });
                e.preventDefault();
            }
        });
        
        document.addEventListener('keyup', function(e) {
            keys[e.key] = false;
        });
        
        console.log('Simple game ready!');
    </script>
</body>
</html>
