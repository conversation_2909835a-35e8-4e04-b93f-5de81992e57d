// Space Fighter Game

// First-time user tracking
const STORAGE_KEY = 'spacefighter_seen_instructions';

function hasSeenInstructions() {
    return localStorage.getItem(STORAGE_KEY) === 'true';
}

function markInstructionsSeen() {
    localStorage.setItem(STORAGE_KEY, 'true');
    console.log('Instructions marked as seen');
}

function showInstructionsForFirstTime() {
    console.log('Showing instructions for first-time user');

    // Show welcome message and "Got it, let's play!" button
    document.getElementById('welcomeMessage').style.display = 'block';
    document.getElementById('instructionsTitle').textContent = 'How to Play';
    document.getElementById('startPlayingBtn').style.display = 'inline-block';
    document.getElementById('backBtn').style.display = 'inline-block';

    showScreen('instructionsScreen');
}

function showInstructionsNormal() {
    console.log('Showing instructions for returning user');

    // Hide welcome message and "Got it, let's play!" button
    document.getElementById('welcomeMessage').style.display = 'none';
    document.getElementById('instructionsTitle').textContent = 'How to Play';
    document.getElementById('startPlayingBtn').style.display = 'none';
    document.getElementById('backBtn').style.display = 'inline-block';

    showScreen('instructionsScreen');
}

function checkUserStatus() {
    const isFirstTime = !hasSeenInstructions();
    console.log('User status - First time:', isFirstTime);

    // Update start button text for first-time users
    const startBtn = document.getElementById('startBtn');
    if (startBtn && isFirstTime) {
        startBtn.innerHTML = '<span>🎮 START TUTORIAL</span>';
        startBtn.title = 'Learn how to play first!';
    } else if (startBtn) {
        startBtn.innerHTML = '<span>🚀 START GAME</span>';
        startBtn.title = 'Jump right into the action!';
    }

    // Add subtle indicator for returning users
    if (!isFirstTime) {
        const subtitle = document.querySelector('.game-subtitle');
        if (subtitle) {
            subtitle.innerHTML = 'Welcome back, Space Defender! 🌟';
        }
    }
}

// Game State
const Game = {
    canvas: null,
    ctx: null,
    width: 800,
    height: 600,
    running: false,
    paused: false,
    score: 0,
    level: 1,
    lives: 3,
    enemiesDestroyed: 0,
    powerUpType: 'normal', // normal, rapid, spread
    powerUpTimer: 0,
    
    // Game objects
    player: null,
    bullets: [],
    enemies: [],
    enemyBullets: [],
    powerUps: [],
    particles: [],
    
    // Input
    keys: {},
    
    // Timing
    lastTime: 0,
    enemySpawnTimer: 0,
    powerUpSpawnTimer: 0
};

// Player class
class Player {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 40;
        this.speed = 5;
        this.shootCooldown = 0;
    }
    
    update() {
        // Movement
        if (Game.keys['ArrowLeft'] && this.x > 0) {
            this.x -= this.speed;
        }
        if (Game.keys['ArrowRight'] && this.x < Game.width - this.width) {
            this.x += this.speed;
        }
        
        // Shooting
        if (Game.keys[' '] && this.shootCooldown <= 0) {
            this.shoot();
            this.shootCooldown = Game.powerUpType === 'rapid' ? 5 : 15;
        }
        
        if (this.shootCooldown > 0) {
            this.shootCooldown--;
        }
    }
    
    shoot() {
        if (Game.powerUpType === 'spread') {
            // Spread shot
            Game.bullets.push(new Bullet(this.x + this.width/2 - 2, this.y, 0, -8));
            Game.bullets.push(new Bullet(this.x + this.width/2 - 2, this.y, -2, -8));
            Game.bullets.push(new Bullet(this.x + this.width/2 - 2, this.y, 2, -8));
        } else {
            // Normal shot
            Game.bullets.push(new Bullet(this.x + this.width/2 - 2, this.y, 0, -8));
        }
        
        playSound('shoot');
    }
    
    draw() {
        const ctx = Game.ctx;

        // Draw engine trail
        ctx.save();
        ctx.globalAlpha = 0.7;
        const trailLength = 15;
        for (let i = 0; i < trailLength; i++) {
            ctx.globalAlpha = (trailLength - i) / trailLength * 0.5;
            ctx.fillStyle = i % 2 === 0 ? '#00d4ff' : '#ff6b35';
            ctx.fillRect(
                this.x + this.width/2 - 2,
                this.y + this.height + i * 3,
                4,
                6
            );
        }
        ctx.restore();

        // Draw player ship with glow
        ctx.save();
        ctx.shadowColor = '#00d4ff';
        ctx.shadowBlur = 15;
        ctx.fillStyle = '#00d4ff';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // Draw ship core
        ctx.shadowColor = '#ff6b35';
        ctx.shadowBlur = 10;
        ctx.fillStyle = '#ff6b35';
        ctx.fillRect(this.x + 8, this.y + 8, this.width - 16, this.height - 16);

        // Draw cockpit
        ctx.shadowBlur = 5;
        ctx.fillStyle = '#fff';
        ctx.fillRect(this.x + this.width/2 - 4, this.y + 5, 8, 12);

        ctx.restore();

        // Animated engine glow
        const engineGlow = Math.sin(Date.now() * 0.02) * 0.3 + 0.7;
        ctx.save();
        ctx.globalAlpha = engineGlow;
        ctx.shadowColor = '#fff';
        ctx.shadowBlur = 20;
        ctx.fillStyle = '#fff';
        ctx.fillRect(this.x + this.width/2 - 3, this.y + this.height, 6, 12);
        ctx.restore();
    }
}

// Bullet class
class Bullet {
    constructor(x, y, vx = 0, vy = -8) {
        this.x = x;
        this.y = y;
        this.vx = vx;
        this.vy = vy;
        this.width = 4;
        this.height = 10;
        this.color = vy < 0 ? '#00d4ff' : '#ff6b35';
    }
    
    update() {
        this.x += this.vx;
        this.y += this.vy;
    }
    
    draw() {
        const ctx = Game.ctx;

        // Draw bullet trail
        ctx.save();
        const trailLength = 8;
        for (let i = 0; i < trailLength; i++) {
            ctx.globalAlpha = (trailLength - i) / trailLength * 0.6;
            ctx.fillStyle = this.color;
            const trailY = this.y + (this.vy > 0 ? -i * 2 : i * 2);
            ctx.fillRect(this.x, trailY, this.width, this.height);
        }
        ctx.restore();

        // Draw main bullet with enhanced glow
        ctx.save();
        ctx.shadowColor = this.color;
        ctx.shadowBlur = 15;
        ctx.fillStyle = this.color;
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // Add core brightness
        ctx.shadowBlur = 5;
        ctx.fillStyle = '#fff';
        ctx.fillRect(this.x + 1, this.y + 1, this.width - 2, this.height - 2);

        ctx.restore();

        // Add sparkle effect
        if (Math.random() < 0.3) {
            ctx.save();
            ctx.fillStyle = '#fff';
            ctx.globalAlpha = 0.8;
            const sparkleX = this.x + Math.random() * this.width;
            const sparkleY = this.y + Math.random() * this.height;
            ctx.fillRect(sparkleX, sparkleY, 1, 1);
            ctx.restore();
        }
    }
    
    isOffScreen() {
        return this.y < -this.height || this.y > Game.height + this.height ||
               this.x < -this.width || this.x > Game.width + this.width;
    }
}

// Enemy class
class Enemy {
    constructor(x, y, type = 'basic') {
        this.x = x;
        this.y = y;
        this.type = type;
        this.width = type === 'boss' ? 60 : 30;
        this.height = type === 'boss' ? 60 : 30;
        this.speed = type === 'boss' ? 1 : 2;
        this.health = type === 'boss' ? 5 : 1;
        this.maxHealth = this.health;
        this.shootTimer = Math.random() * 60;
        this.points = type === 'boss' ? 100 : 10;
    }
    
    update() {
        this.y += this.speed;
        
        // Shooting
        this.shootTimer--;
        if (this.shootTimer <= 0 && this.y > 50) {
            Game.enemyBullets.push(new Bullet(this.x + this.width/2, this.y + this.height, 0, 4));
            this.shootTimer = 60 + Math.random() * 60;
        }
    }
    
    draw() {
        const ctx = Game.ctx;
        const time = Date.now() * 0.01;

        if (this.type === 'boss') {
            // Animated boss enemy with pulsing glow
            const pulseScale = 1 + Math.sin(time * 0.5) * 0.1;
            const glowIntensity = Math.sin(time) * 0.3 + 0.7;

            ctx.save();
            ctx.translate(this.x + this.width/2, this.y + this.height/2);
            ctx.scale(pulseScale, pulseScale);

            // Boss glow aura
            ctx.shadowColor = '#ff6b35';
            ctx.shadowBlur = 25 * glowIntensity;
            ctx.fillStyle = '#ff6b35';
            ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);

            // Boss core
            ctx.shadowBlur = 10;
            ctx.fillStyle = '#fff';
            ctx.fillRect(-this.width/2 + 8, -this.height/2 + 8, this.width - 16, this.height - 16);

            // Animated energy core
            ctx.shadowBlur = 15;
            ctx.fillStyle = `hsl(${time * 10 % 360}, 100%, 70%)`;
            ctx.fillRect(-8, -8, 16, 16);

            ctx.restore();

            // Animated health bar
            const healthPercent = this.health / this.maxHealth;
            ctx.fillStyle = '#333';
            ctx.fillRect(this.x, this.y - 15, this.width, 8);

            const healthColor = healthPercent > 0.5 ? '#27ae60' : '#e74c3c';
            ctx.fillStyle = healthColor;
            ctx.shadowColor = healthColor;
            ctx.shadowBlur = 5;
            ctx.fillRect(this.x, this.y - 15, this.width * healthPercent, 8);
            ctx.shadowBlur = 0;
        } else {
            // Enhanced basic enemy with rotation and glow
            const rotation = Math.sin(time + this.x * 0.01) * 0.2;

            ctx.save();
            ctx.translate(this.x + this.width/2, this.y + this.height/2);
            ctx.rotate(rotation);

            // Enemy glow
            ctx.shadowColor = '#ff6b35';
            ctx.shadowBlur = 12;
            ctx.fillStyle = '#ff6b35';
            ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);

            // Enemy core
            ctx.shadowBlur = 5;
            ctx.fillStyle = '#fff';
            ctx.fillRect(-this.width/2 + 4, -this.height/2 + 4, this.width - 8, this.height - 8);

            // Animated center dot
            const dotSize = 2 + Math.sin(time * 2) * 1;
            ctx.fillStyle = '#00d4ff';
            ctx.fillRect(-dotSize/2, -dotSize/2, dotSize, dotSize);

            ctx.restore();
        }
    }
    
    isOffScreen() {
        return this.y > Game.height + this.height;
    }
}

// PowerUp class
class PowerUp {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.type = type; // 'rapid', 'spread', 'life'
        this.width = 20;
        this.height = 20;
        this.speed = 2;
        this.rotation = 0;
    }
    
    update() {
        this.y += this.speed;
        this.rotation += 0.1;
    }
    
    draw() {
        const ctx = Game.ctx;
        const time = Date.now() * 0.01;
        const pulse = Math.sin(time * 2) * 0.3 + 1;

        ctx.save();
        ctx.translate(this.x + this.width/2, this.y + this.height/2);
        ctx.rotate(this.rotation);
        ctx.scale(pulse, pulse);

        let color, glowColor;
        switch(this.type) {
            case 'rapid':
                color = '#00d4ff';
                glowColor = 'rgba(0, 212, 255, 0.8)';
                break;
            case 'spread':
                color = '#ff6b35';
                glowColor = 'rgba(255, 107, 53, 0.8)';
                break;
            case 'life':
                color = '#27ae60';
                glowColor = 'rgba(39, 174, 96, 0.8)';
                break;
        }

        // Draw outer glow ring
        ctx.beginPath();
        ctx.arc(0, 0, this.width/2 + 8, 0, Math.PI * 2);
        ctx.fillStyle = glowColor;
        ctx.globalAlpha = 0.3;
        ctx.fill();

        // Draw main power-up with intense glow
        ctx.globalAlpha = 1;
        ctx.shadowColor = color;
        ctx.shadowBlur = 20;
        ctx.fillStyle = color;
        ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);

        // Draw inner bright core
        ctx.shadowBlur = 8;
        ctx.fillStyle = '#fff';
        ctx.fillRect(-this.width/2 + 4, -this.height/2 + 4, this.width - 8, this.height - 8);

        // Draw type indicator
        ctx.shadowBlur = 0;
        ctx.fillStyle = color;
        ctx.font = 'bold 12px Orbitron';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        const symbol = this.type === 'rapid' ? 'R' : this.type === 'spread' ? 'S' : '+';
        ctx.fillText(symbol, 0, 0);

        ctx.restore();

        // Draw floating particles around power-up
        for (let i = 0; i < 3; i++) {
            const angle = time + i * (Math.PI * 2 / 3);
            const radius = 25 + Math.sin(time * 3 + i) * 5;
            const particleX = this.x + this.width/2 + Math.cos(angle) * radius;
            const particleY = this.y + this.height/2 + Math.sin(angle) * radius;

            ctx.save();
            ctx.globalAlpha = 0.7;
            ctx.fillStyle = color;
            ctx.shadowColor = color;
            ctx.shadowBlur = 8;
            ctx.fillRect(particleX - 1, particleY - 1, 2, 2);
            ctx.restore();
        }
    }
    
    isOffScreen() {
        return this.y > Game.height + this.height;
    }
}

// Enhanced Particle class for spectacular explosions
class Particle {
    constructor(x, y, color = '#ff6b35', type = 'explosion') {
        this.x = x;
        this.y = y;
        this.vx = (Math.random() - 0.5) * 12;
        this.vy = (Math.random() - 0.5) * 12;
        this.life = 40 + Math.random() * 20;
        this.maxLife = this.life;
        this.color = color;
        this.size = Math.random() * 6 + 2;
        this.type = type;
        this.rotation = Math.random() * Math.PI * 2;
        this.rotationSpeed = (Math.random() - 0.5) * 0.3;
        this.gravity = type === 'explosion' ? 0.1 : 0;
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.vy += this.gravity;
        this.life--;
        this.vx *= 0.97;
        this.vy *= 0.97;
        this.rotation += this.rotationSpeed;

        // Shrink over time
        this.size *= 0.99;
    }

    draw() {
        const ctx = Game.ctx;
        const alpha = this.life / this.maxLife;

        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);

        if (this.type === 'explosion') {
            // Explosion particle with glow
            ctx.shadowColor = this.color;
            ctx.shadowBlur = 10;
            ctx.fillStyle = this.color;
            ctx.fillRect(-this.size/2, -this.size/2, this.size, this.size);

            // Bright center
            ctx.shadowBlur = 5;
            ctx.fillStyle = '#fff';
            ctx.fillRect(-this.size/4, -this.size/4, this.size/2, this.size/2);
        } else if (this.type === 'spark') {
            // Spark particle
            ctx.strokeStyle = this.color;
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(-this.size, 0);
            ctx.lineTo(this.size, 0);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(0, -this.size);
            ctx.lineTo(0, this.size);
            ctx.stroke();
        }

        ctx.restore();
    }

    isDead() {
        return this.life <= 0 || this.size < 0.5;
    }
}

// Initialize game
function initGame() {
    console.log('Initializing game...');

    Game.canvas = document.getElementById('gameCanvas');
    if (!Game.canvas) {
        console.error('Canvas not found!');
        return;
    }

    Game.ctx = Game.canvas.getContext('2d');
    if (!Game.ctx) {
        console.error('Canvas context not available!');
        return;
    }

    console.log('Canvas found and context created');

    // Set canvas size for mobile
    resizeCanvas();

    // Initialize player
    Game.player = new Player(Game.width/2 - 20, Game.height - 60);
    console.log('Player created at:', Game.player.x, Game.player.y);

    // Reset game state
    Game.score = 0;
    Game.level = 1;
    Game.lives = 3;
    Game.enemiesDestroyed = 0;
    Game.powerUpType = 'normal';
    Game.powerUpTimer = 0;

    // Clear arrays
    Game.bullets = [];
    Game.enemies = [];
    Game.enemyBullets = [];
    Game.powerUps = [];
    Game.particles = [];
    Game.scorePopups = [];
    Game.levelTransition = null;

    updateUI();

    console.log('Game initialized successfully');
}

function resizeCanvas() {
    const container = document.getElementById('gameScreen');
    const maxWidth = Math.min(800, window.innerWidth - 40);
    const maxHeight = Math.min(600, window.innerHeight - 200);
    
    if (window.innerWidth <= 768) {
        Game.canvas.style.width = maxWidth + 'px';
        Game.canvas.style.height = (maxWidth * 0.75) + 'px';
    }
}

// Game loop
function gameLoop(currentTime) {
    if (!Game.running || Game.paused) {
        console.log('Game loop stopped - running:', Game.running, 'paused:', Game.paused);
        return;
    }

    const deltaTime = currentTime - Game.lastTime;
    Game.lastTime = currentTime;

    try {
        update(deltaTime);
        draw();
        requestAnimationFrame(gameLoop);
    } catch (error) {
        console.error('Error in game loop:', error);
        Game.running = false;
    }
}

function update(deltaTime) {
    // Update player
    Game.player.update();
    
    // Update bullets
    Game.bullets.forEach(bullet => bullet.update());
    Game.bullets = Game.bullets.filter(bullet => !bullet.isOffScreen());
    
    // Update enemies
    Game.enemies.forEach(enemy => enemy.update());
    Game.enemies = Game.enemies.filter(enemy => !enemy.isOffScreen());
    
    // Update enemy bullets
    Game.enemyBullets.forEach(bullet => bullet.update());
    Game.enemyBullets = Game.enemyBullets.filter(bullet => !bullet.isOffScreen());
    
    // Update power-ups
    Game.powerUps.forEach(powerUp => powerUp.update());
    Game.powerUps = Game.powerUps.filter(powerUp => !powerUp.isOffScreen());
    
    // Update particles
    Game.particles.forEach(particle => particle.update());
    Game.particles = Game.particles.filter(particle => !particle.isDead());
    
    // Spawn enemies
    Game.enemySpawnTimer++;
    if (Game.enemySpawnTimer > 60 - Game.level * 5) {
        spawnEnemy();
        Game.enemySpawnTimer = 0;
    }
    
    // Spawn power-ups
    Game.powerUpSpawnTimer++;
    if (Game.powerUpSpawnTimer > 600) { // Every 10 seconds
        spawnPowerUp();
        Game.powerUpSpawnTimer = 0;
    }
    
    // Update power-up timer
    if (Game.powerUpTimer > 0) {
        Game.powerUpTimer--;
        if (Game.powerUpTimer <= 0) {
            Game.powerUpType = 'normal';
            updateUI();
        }
    }

    // Check collisions
    checkCollisions();

    // Level progression
    if (Game.enemiesDestroyed > 0 && Game.enemiesDestroyed % 10 === 0 && Game.enemies.length === 0) {
        Game.level++;
        Game.levelTransition = 120; // Show level text for 2 seconds
        updateUI();
    }

    // Update score popups
    if (Game.scorePopups) {
        Game.scorePopups.forEach((popup, index) => {
            popup.y += popup.vy;
            popup.life--;
            if (popup.life <= 0) {
                Game.scorePopups.splice(index, 1);
            }
        });
    }
}

function draw() {
    const ctx = Game.ctx;

    // Clear canvas with animated space background
    const gradient = ctx.createLinearGradient(0, 0, 0, Game.height);
    gradient.addColorStop(0, '#000011');
    gradient.addColorStop(0.5, '#000033');
    gradient.addColorStop(1, '#000000');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, Game.width, Game.height);

    // Draw animated background
    drawStars();

    // Draw game objects with enhanced effects
    Game.player.draw();
    Game.bullets.forEach(bullet => bullet.draw());
    Game.enemies.forEach(enemy => enemy.draw());
    Game.enemyBullets.forEach(bullet => bullet.draw());
    Game.powerUps.forEach(powerUp => powerUp.draw());
    Game.particles.forEach(particle => particle.draw());

    // Draw score popups
    drawScorePopups();

    // Draw screen effects
    drawScreenEffects();
}

function drawScorePopups() {
    if (!Game.scorePopups) return;

    const ctx = Game.ctx;

    Game.scorePopups.forEach((popup, index) => {
        popup.y += popup.vy;
        popup.life--;

        const alpha = popup.life / popup.maxLife;
        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = '#ffff00';
        ctx.shadowColor = '#ffff00';
        ctx.shadowBlur = 10;
        ctx.font = 'bold 16px Orbitron';
        ctx.textAlign = 'center';
        ctx.fillText(`+${popup.points}`, popup.x, popup.y);
        ctx.restore();

        if (popup.life <= 0) {
            Game.scorePopups.splice(index, 1);
        }
    });
}

function drawScreenEffects() {
    const ctx = Game.ctx;

    // Draw power-up effect overlay
    if (Game.powerUpType !== 'normal' && Game.powerUpTimer > 0) {
        const alpha = Math.sin(Date.now() * 0.01) * 0.1 + 0.05;
        const color = Game.powerUpType === 'rapid' ? '#00d4ff' : '#ff6b35';

        ctx.save();
        ctx.globalAlpha = alpha;
        ctx.fillStyle = color;
        ctx.fillRect(0, 0, Game.width, Game.height);
        ctx.restore();

        // Draw power-up border effect
        ctx.save();
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        ctx.shadowColor = color;
        ctx.shadowBlur = 10;
        ctx.strokeRect(2, 2, Game.width - 4, Game.height - 4);
        ctx.restore();
    }

    // Draw level transition effect
    if (Game.levelTransition) {
        const progress = Game.levelTransition / 60;
        ctx.save();
        ctx.globalAlpha = 1 - Math.abs(progress - 0.5) * 2;
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 48px Orbitron';
        ctx.textAlign = 'center';
        ctx.shadowColor = '#00d4ff';
        ctx.shadowBlur = 20;
        ctx.fillText(`LEVEL ${Game.level}`, Game.width/2, Game.height/2);
        ctx.restore();

        Game.levelTransition--;
        if (Game.levelTransition <= 0) {
            Game.levelTransition = null;
        }
    }
}

function drawStars() {
    const ctx = Game.ctx;
    const time = Date.now() * 0.001;

    // Draw animated nebula background
    drawNebula();

    // Draw twinkling stars
    for (let i = 0; i < 80; i++) {
        const x = (i * 47) % Game.width;
        const y = (i * 23 + time * 20) % Game.height;
        const size = Math.sin(i * 0.5) * 2 + 1;
        const twinkle = Math.sin(time * 3 + i) * 0.5 + 0.5;

        // Star color variation
        const colors = ['#fff', '#00d4ff', '#ff6b35', '#ffff00'];
        const colorIndex = Math.floor(i / 20) % colors.length;

        ctx.save();
        ctx.globalAlpha = twinkle * 0.8;
        ctx.fillStyle = colors[colorIndex];
        ctx.shadowColor = colors[colorIndex];
        ctx.shadowBlur = size * 2;

        // Draw star with cross shape
        ctx.fillRect(x - size/2, y - size * 2, size, size * 4);
        ctx.fillRect(x - size * 2, y - size/2, size * 4, size);

        ctx.restore();
    }

    // Draw distant galaxies
    for (let i = 0; i < 5; i++) {
        const x = (i * 150 + time * 5) % (Game.width + 100) - 50;
        const y = (i * 80 + 50) % Game.height;
        const radius = 20 + i * 5;

        ctx.save();
        ctx.globalAlpha = 0.1 + Math.sin(time + i) * 0.05;

        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
        gradient.addColorStop(0, i % 2 === 0 ? '#00d4ff' : '#ff6b35');
        gradient.addColorStop(1, 'transparent');

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

function drawNebula() {
    const ctx = Game.ctx;
    const time = Date.now() * 0.0005;

    // Create animated nebula clouds
    for (let i = 0; i < 3; i++) {
        const x = (i * 200 + Math.sin(time + i) * 50) % (Game.width + 100) - 50;
        const y = (i * 150 + Math.cos(time * 0.7 + i) * 30) % (Game.height + 100) - 50;
        const radius = 80 + Math.sin(time * 2 + i) * 20;

        ctx.save();
        ctx.globalAlpha = 0.05 + Math.sin(time * 3 + i) * 0.03;

        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);
        const colors = ['#ff6b35', '#00d4ff', '#9b59b6'];
        const color = colors[i % colors.length];

        gradient.addColorStop(0, color);
        gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.1)');
        gradient.addColorStop(1, 'transparent');

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(x, y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
    }
}

function spawnEnemy() {
    const x = Math.random() * (Game.width - 30);
    const type = Math.random() < 0.1 && Game.level > 3 ? 'boss' : 'basic';
    Game.enemies.push(new Enemy(x, -30, type));
}

function spawnPowerUp() {
    const x = Math.random() * (Game.width - 20);
    const types = ['rapid', 'spread', 'life'];
    const type = types[Math.floor(Math.random() * types.length)];
    Game.powerUps.push(new PowerUp(x, -20, type));
}

function checkCollisions() {
    // Player bullets vs enemies
    Game.bullets.forEach((bullet, bulletIndex) => {
        Game.enemies.forEach((enemy, enemyIndex) => {
            if (isColliding(bullet, enemy)) {
                // Create explosion particles
                const explosionSize = enemy.type === 'boss' ? 'boss' : 'normal';
                createExplosion(enemy.x + enemy.width/2, enemy.y + enemy.height/2, explosionSize);

                enemy.health--;
                Game.bullets.splice(bulletIndex, 1);

                if (enemy.health <= 0) {
                    Game.score += enemy.points;
                    Game.enemiesDestroyed++;
                    Game.enemies.splice(enemyIndex, 1);
                    playSound('explosion');

                    // Create score popup
                    createScorePopup(enemy.x + enemy.width/2, enemy.y + enemy.height/2, enemy.points);

                    // Chance to drop power-up
                    if (Math.random() < 0.15) {
                        const types = ['rapid', 'spread', 'life'];
                        const type = types[Math.floor(Math.random() * types.length)];
                        Game.powerUps.push(new PowerUp(enemy.x, enemy.y, type));
                    }
                } else {
                    // Enemy hit but not destroyed - create smaller explosion
                    createHitEffect(enemy.x + enemy.width/2, enemy.y + enemy.height/2);
                }

                updateUI();
            }
        });
    });

    // Enemy bullets vs player
    Game.enemyBullets.forEach((bullet, bulletIndex) => {
        if (isColliding(bullet, Game.player)) {
            Game.enemyBullets.splice(bulletIndex, 1);
            createExplosion(bullet.x, bullet.y, 'small');
            takeDamage();
        }
    });

    // Enemies vs player
    Game.enemies.forEach((enemy, enemyIndex) => {
        if (isColliding(enemy, Game.player)) {
            Game.enemies.splice(enemyIndex, 1);
            takeDamage();
            createExplosion(enemy.x + enemy.width/2, enemy.y + enemy.height/2);
        }
    });

    // Power-ups vs player
    Game.powerUps.forEach((powerUp, powerUpIndex) => {
        if (isColliding(powerUp, Game.player)) {
            Game.powerUps.splice(powerUpIndex, 1);
            collectPowerUp(powerUp.type);
            createCollectEffect(powerUp.x + powerUp.width/2, powerUp.y + powerUp.height/2, powerUp.type);
        }
    });
}

function createScorePopup(x, y, points) {
    const popup = {
        x: x,
        y: y,
        points: points,
        life: 60,
        maxLife: 60,
        vy: -2
    };

    Game.scorePopups = Game.scorePopups || [];
    Game.scorePopups.push(popup);
}

function createHitEffect(x, y) {
    for (let i = 0; i < 5; i++) {
        Game.particles.push(new Particle(x, y, '#ffff00', 'spark'));
    }
}

function createCollectEffect(x, y, type) {
    const color = type === 'rapid' ? '#00d4ff' : type === 'spread' ? '#ff6b35' : '#27ae60';

    for (let i = 0; i < 12; i++) {
        const angle = (i / 12) * Math.PI * 2;
        const particle = new Particle(x, y, color, 'spark');
        particle.vx = Math.cos(angle) * 6;
        particle.vy = Math.sin(angle) * 6;
        Game.particles.push(particle);
    }
}

function isColliding(obj1, obj2) {
    return obj1.x < obj2.x + obj2.width &&
           obj1.x + obj1.width > obj2.x &&
           obj1.y < obj2.y + obj2.height &&
           obj1.y + obj1.height > obj2.y;
}

function createExplosion(x, y, size = 'normal') {
    const particleCount = size === 'boss' ? 25 : 15;
    const colors = ['#ff6b35', '#00d4ff', '#fff', '#ffff00'];

    // Main explosion particles
    for (let i = 0; i < particleCount; i++) {
        const color = colors[Math.floor(Math.random() * colors.length)];
        Game.particles.push(new Particle(x, y, color, 'explosion'));
    }

    // Add spark particles
    for (let i = 0; i < 8; i++) {
        Game.particles.push(new Particle(x, y, '#fff', 'spark'));
    }

    // Screen shake effect
    if (size === 'boss') {
        screenShake(10, 300);
    } else {
        screenShake(5, 150);
    }

    // Flash effect
    createFlashEffect();
}

function screenShake(intensity, duration) {
    const canvas = Game.canvas;
    const originalTransform = canvas.style.transform;

    let startTime = Date.now();

    function shake() {
        const elapsed = Date.now() - startTime;
        const progress = elapsed / duration;

        if (progress < 1) {
            const currentIntensity = intensity * (1 - progress);
            const offsetX = (Math.random() - 0.5) * currentIntensity;
            const offsetY = (Math.random() - 0.5) * currentIntensity;

            canvas.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
            requestAnimationFrame(shake);
        } else {
            canvas.style.transform = originalTransform;
        }
    }

    shake();
}

function createFlashEffect() {
    const flash = document.createElement('div');
    flash.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
        pointer-events: none;
        z-index: 1000;
        opacity: 1;
        transition: opacity 0.2s ease;
    `;

    document.getElementById('gameScreen').appendChild(flash);

    setTimeout(() => {
        flash.style.opacity = '0';
        setTimeout(() => {
            if (flash.parentNode) {
                flash.parentNode.removeChild(flash);
            }
        }, 200);
    }, 50);
}

function collectPowerUp(type) {
    switch(type) {
        case 'rapid':
            Game.powerUpType = 'rapid';
            Game.powerUpTimer = 300; // 5 seconds
            break;
        case 'spread':
            Game.powerUpType = 'spread';
            Game.powerUpTimer = 300;
            break;
        case 'life':
            Game.lives++;
            break;
    }
    updateUI();
    playSound('powerup');
}

function takeDamage() {
    Game.lives--;
    updateUI();
    
    // Screen flash effect
    const flash = document.createElement('div');
    flash.className = 'screen-flash active';
    document.getElementById('gameScreen').appendChild(flash);
    
    setTimeout(() => {
        flash.remove();
    }, 100);
    
    if (Game.lives <= 0) {
        gameOver();
    }
}

function updateUI() {
    try {
        const scoreEl = document.getElementById('score');
        const levelEl = document.getElementById('level');
        const livesEl = document.getElementById('lives');
        const powerEl = document.getElementById('power');

        if (scoreEl) scoreEl.textContent = Game.score;
        if (levelEl) levelEl.textContent = Game.level;
        if (livesEl) livesEl.textContent = Game.lives;

        let powerText = 'Normal';
        if (Game.powerUpType === 'rapid') powerText = 'Rapid Fire';
        else if (Game.powerUpType === 'spread') powerText = 'Spread Shot';

        if (powerEl) powerEl.textContent = powerText;

        console.log('UI updated - Score:', Game.score, 'Lives:', Game.lives);
    } catch (error) {
        console.error('Error updating UI:', error);
    }
}

function startGame() {
    console.log('Starting game...');

    try {
        showScreen('gameScreen');
        console.log('Screen changed to gameScreen');

        initGame();
        console.log('Game initialized');

        Game.running = true;
        Game.paused = false;
        Game.lastTime = performance.now();

        console.log('Starting game loop...');
        requestAnimationFrame(gameLoop);

        console.log('Game started successfully!');
    } catch (error) {
        console.error('Error starting game:', error);
    }
}

function pauseGame() {
    Game.paused = true;
    showScreen('pauseScreen');
}

function resumeGame() {
    Game.paused = false;
    showScreen('gameScreen');
    Game.lastTime = performance.now();
    requestAnimationFrame(gameLoop);
}

function gameOver() {
    Game.running = false;
    
    // Update final stats
    document.getElementById('finalScore').textContent = Game.score;
    document.getElementById('finalLevel').textContent = Game.level;
    document.getElementById('enemiesDestroyed').textContent = Game.enemiesDestroyed;
    
    // Check for high score
    const highScores = getHighScores();
    const isHighScore = highScores.length < 10 || Game.score > highScores[highScores.length - 1].score;
    
    if (isHighScore) {
        document.getElementById('newHighScore').style.display = 'block';
        document.getElementById('nameInput').style.display = 'block';
    }
    
    showScreen('gameOverScreen');
}

// Screen management
function showScreen(screenId) {
    console.log('Showing screen:', screenId);

    const targetScreen = document.getElementById(screenId);
    if (!targetScreen) {
        console.error('Screen not found:', screenId);
        return;
    }

    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });

    targetScreen.classList.add('active');
    console.log('Screen changed successfully to:', screenId);
}

// High score management
function getHighScores() {
    return JSON.parse(localStorage.getItem('spaceHighScores') || '[]');
}

function saveHighScore(name, score) {
    const highScores = getHighScores();
    highScores.push({ name, score, date: new Date().toLocaleDateString() });
    highScores.sort((a, b) => b.score - a.score);
    highScores.splice(10); // Keep only top 10
    localStorage.setItem('spaceHighScores', JSON.stringify(highScores));
    loadHighScores();
}

function loadHighScores() {
    const highScores = getHighScores();
    const list = document.getElementById('highScoreList');
    
    if (highScores.length === 0) {
        list.innerHTML = '<div style="text-align: center; color: #666; padding: 40px;">No high scores yet!<br>Be the first to set a record!</div>';
        return;
    }
    
    list.innerHTML = highScores.map((score, index) => `
        <div class="score-item">
            <span class="score-rank">#${index + 1}</span>
            <span class="score-name">${score.name}</span>
            <span class="score-points">${score.score.toLocaleString()}</span>
        </div>
    `).join('');
}

// Sound effects (simple beep sounds)
function playSound(type) {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    switch(type) {
        case 'shoot':
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            oscillator.start();
            oscillator.stop(audioContext.currentTime + 0.1);
            break;
        case 'explosion':
            oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.2, audioContext.currentTime);
            oscillator.start();
            oscillator.stop(audioContext.currentTime + 0.3);
            break;
        case 'powerup':
            oscillator.frequency.setValueAtTime(1200, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            oscillator.start();
            oscillator.stop(audioContext.currentTime + 0.2);
            break;
    }
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing game...');

    // Check user status and update UI accordingly
    checkUserStatus();

    // Check if elements exist
    const startBtn = document.getElementById('startBtn');
    console.log('Start button found:', startBtn);

    if (!startBtn) {
        console.error('Start button not found!');
        return;
    }

    // Menu navigation - Enhanced for first-time users
    startBtn.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Start button clicked!');

        // Check if this is the user's first time
        if (!hasSeenInstructions()) {
            console.log('First-time user detected, showing instructions...');
            showInstructionsForFirstTime();
            return;
        }

        // Verify all required elements exist for returning users
        const gameScreen = document.getElementById('gameScreen');
        const gameCanvas = document.getElementById('gameCanvas');

        if (!gameScreen) {
            console.error('Game screen not found!');
            return;
        }

        if (!gameCanvas) {
            console.error('Game canvas not found!');
            return;
        }

        console.log('Returning user - starting game directly...');
        startGame();
    });
    document.getElementById('instructionsBtn').addEventListener('click', () => {
        console.log('Instructions button clicked (normal view)');
        showInstructionsNormal();
    });

    document.getElementById('highScoreBtn').addEventListener('click', () => {
        loadHighScores();
        showScreen('highScoreScreen');
    });

    // Instructions screen buttons
    document.getElementById('backBtn').addEventListener('click', () => {
        console.log('Back button clicked from instructions');
        showScreen('startScreen');
    });

    document.getElementById('startPlayingBtn').addEventListener('click', () => {
        console.log('Start Playing button clicked - marking instructions as seen');
        markInstructionsSeen();
        startGame();
    });

    document.getElementById('backFromScoresBtn').addEventListener('click', () => showScreen('startScreen'));
    
    // Game controls
    document.getElementById('pauseBtn').addEventListener('click', pauseGame);
    document.getElementById('resumeBtn').addEventListener('click', resumeGame);
    document.getElementById('restartBtn').addEventListener('click', startGame);
    document.getElementById('quitBtn').addEventListener('click', () => {
        Game.running = false;
        showScreen('startScreen');
    });
    
    // Game over controls
    document.getElementById('playAgainBtn').addEventListener('click', startGame);
    document.getElementById('mainMenuBtn').addEventListener('click', () => showScreen('startScreen'));
    
    // High score saving
    function savePlayerScore() {
        const name = document.getElementById('playerName').value.trim() || 'Anonymous';
        saveHighScore(name, Game.score);
        document.getElementById('nameInput').style.display = 'none';
        document.getElementById('newHighScore').style.display = 'none';
    }

    document.getElementById('saveScoreBtn').addEventListener('click', savePlayerScore);

    // Add Enter key support for name input
    document.getElementById('playerName').addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            savePlayerScore();
        }
    });
    
    // Clear high scores
    document.getElementById('clearScoresBtn').addEventListener('click', function() {
        if (confirm('Are you sure you want to clear all high scores?')) {
            localStorage.removeItem('spaceHighScores');
            loadHighScores();
        }
    });

    // Debug function to reset first-time status (Ctrl+Shift+R)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.shiftKey && e.key === 'R') {
            localStorage.removeItem(STORAGE_KEY);
            console.log('First-time status reset! Refresh page to test first-time experience.');
            alert('First-time status reset! Refresh the page to test the first-time user experience.');
        }
    });
    
    // Keyboard controls
    document.addEventListener('keydown', function(e) {
        Game.keys[e.key] = true;
        
        if (e.key === 'p' || e.key === 'P') {
            if (Game.running && !Game.paused) {
                pauseGame();
            } else if (Game.paused) {
                resumeGame();
            }
        }
        
        e.preventDefault();
    });
    
    document.addEventListener('keyup', function(e) {
        Game.keys[e.key] = false;
    });
    
    // Mobile controls
    document.getElementById('moveLeftBtn').addEventListener('touchstart', () => Game.keys['ArrowLeft'] = true);
    document.getElementById('moveLeftBtn').addEventListener('touchend', () => Game.keys['ArrowLeft'] = false);
    document.getElementById('moveRightBtn').addEventListener('touchstart', () => Game.keys['ArrowRight'] = true);
    document.getElementById('moveRightBtn').addEventListener('touchend', () => Game.keys['ArrowRight'] = false);
    document.getElementById('shootBtn').addEventListener('touchstart', () => Game.keys[' '] = true);
    document.getElementById('shootBtn').addEventListener('touchend', () => Game.keys[' '] = false);
    
    // Prevent scrolling on mobile
    document.addEventListener('touchmove', function(e) {
        e.preventDefault();
    }, { passive: false });
    
    // Window resize
    window.addEventListener('resize', resizeCanvas);
    
    // Test button functionality
    setTimeout(() => {
        const startBtn = document.getElementById('startBtn');
        if (startBtn) {
            console.log('Start button test - element exists and is clickable');
            console.log('Button styles:', window.getComputedStyle(startBtn).pointerEvents);
        }
    }, 1000);

    console.log('Space Fighter game loaded successfully!');
});

// Additional game functions

// Mobile touch controls with better handling
function initMobileControls() {
    const moveLeftBtn = document.getElementById('moveLeftBtn');
    const moveRightBtn = document.getElementById('moveRightBtn');
    const shootBtn = document.getElementById('shootBtn');

    // Continuous movement while holding
    let leftInterval, rightInterval, shootInterval;

    moveLeftBtn.addEventListener('touchstart', function(e) {
        e.preventDefault();
        Game.keys['ArrowLeft'] = true;
        leftInterval = setInterval(() => {
            Game.keys['ArrowLeft'] = true;
        }, 16);
    });

    moveLeftBtn.addEventListener('touchend', function(e) {
        e.preventDefault();
        Game.keys['ArrowLeft'] = false;
        clearInterval(leftInterval);
    });

    moveRightBtn.addEventListener('touchstart', function(e) {
        e.preventDefault();
        Game.keys['ArrowRight'] = true;
        rightInterval = setInterval(() => {
            Game.keys['ArrowRight'] = true;
        }, 16);
    });

    moveRightBtn.addEventListener('touchend', function(e) {
        e.preventDefault();
        Game.keys['ArrowRight'] = false;
        clearInterval(rightInterval);
    });

    shootBtn.addEventListener('touchstart', function(e) {
        e.preventDefault();
        Game.keys[' '] = true;
        shootInterval = setInterval(() => {
            Game.keys[' '] = true;
        }, 16);
    });

    shootBtn.addEventListener('touchend', function(e) {
        e.preventDefault();
        Game.keys[' '] = false;
        clearInterval(shootInterval);
    });
}

// Initialize mobile controls
initMobileControls();

// Game difficulty scaling
function getDifficultyMultiplier() {
    return 1 + (Game.level - 1) * 0.2;
}

// Enhanced enemy AI
function updateEnemyAI() {
    Game.enemies.forEach(enemy => {
        // Boss enemies move side to side
        if (enemy.type === 'boss') {
            enemy.x += Math.sin(Date.now() * 0.005) * 2;
            enemy.x = Math.max(0, Math.min(Game.width - enemy.width, enemy.x));
        }

        // Increase shooting frequency with level
        if (Math.random() < 0.02 * getDifficultyMultiplier()) {
            if (enemy.shootTimer <= 0) {
                Game.enemyBullets.push(new Bullet(
                    enemy.x + enemy.width/2,
                    enemy.y + enemy.height,
                    0,
                    3 * getDifficultyMultiplier()
                ));
                enemy.shootTimer = 30;
            }
        }
    });
}

// Add to update function
const originalUpdate = update;
update = function(deltaTime) {
    originalUpdate(deltaTime);
    updateEnemyAI();
};

// Cheat codes (for testing)
document.addEventListener('keydown', function(e) {
    // Secret cheat codes
    if (e.ctrlKey && e.key === 'l') {
        Game.lives += 5;
        updateUI();
    }
    if (e.ctrlKey && e.key === 's') {
        Game.score += 1000;
        updateUI();
    }
});

console.log('🚀 Space Fighter - Ready to defend the galaxy!');
