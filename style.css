/* Space Fighter Game Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Orbitron', monospace;
    background: #000;
    color: #fff;
    overflow: hidden;
    user-select: none;
}

#gameContainer {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    justify-content: center;
    background: radial-gradient(ellipse at center, #1a1a2e 0%, #000 100%);
}

.screen.active {
    display: flex;
}

.screen.overlay {
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

/* Enhanced Animated Stars Background */
.stars-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(2px 2px at 20px 30px, #fff, transparent),
        radial-gradient(2px 2px at 40px 70px, #00d4ff, transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, #ff6b35, transparent),
        radial-gradient(2px 2px at 160px 30px, #fff, transparent),
        radial-gradient(3px 3px at 200px 50px, #00d4ff, transparent),
        radial-gradient(1px 1px at 250px 90px, #ff6b35, transparent);
    background-repeat: repeat;
    background-size: 300px 150px;
    animation: stars 25s linear infinite, starTwinkle 3s ease-in-out infinite;
    opacity: 0.8;
    pointer-events: none;
    z-index: 0;
}

@keyframes stars {
    from { transform: translateY(0); }
    to { transform: translateY(-150px); }
}

@keyframes starTwinkle {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 0.4; }
}

/* Additional star layers for depth */
.stars-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(1px 1px at 50px 20px, #fff, transparent),
        radial-gradient(1px 1px at 150px 60px, #00d4ff, transparent),
        radial-gradient(2px 2px at 300px 40px, #ff6b35, transparent);
    background-repeat: repeat;
    background-size: 400px 200px;
    animation: stars 35s linear infinite reverse;
    opacity: 0.6;
}

.stars-bg::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(1px 1px at 80px 80px, #fff, transparent),
        radial-gradient(1px 1px at 180px 20px, #00d4ff, transparent);
    background-repeat: repeat;
    background-size: 250px 120px;
    animation: stars 15s linear infinite;
    opacity: 0.5;
}

/* Start Screen */
.title-container {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
    z-index: 10;
}

.logo-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.space-logo {
    font-size: clamp(3rem, 6vw, 5rem);
    animation: logoFloat 3s ease-in-out infinite, logoGlow 2s ease-in-out infinite;
    filter: drop-shadow(0 0 20px currentColor);
    transition: all 0.3s ease;
}

.space-logo:first-child {
    animation-delay: 0s;
    color: #00d4ff;
}

.space-logo:last-child {
    animation-delay: 1s;
    color: #ff6b35;
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
    }
    25% {
        transform: translateY(-10px) rotate(5deg) scale(1.1);
    }
    50% {
        transform: translateY(-15px) rotate(0deg) scale(1.15);
    }
    75% {
        transform: translateY(-10px) rotate(-5deg) scale(1.1);
    }
}

@keyframes logoGlow {
    0%, 100% {
        filter: drop-shadow(0 0 20px currentColor) brightness(1);
    }
    50% {
        filter: drop-shadow(0 0 40px currentColor) brightness(1.3);
    }
}

.space-logo:hover {
    transform: scale(1.3) rotate(360deg);
    transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Additional space-themed decorative elements */
.title-container::before {
    content: '⭐';
    position: absolute;
    top: -20px;
    left: 20%;
    font-size: 2rem;
    color: #fff;
    animation: starTwinkle 2s ease-in-out infinite;
    animation-delay: 0.5s;
}

.title-container::after {
    content: '✨';
    position: absolute;
    top: -10px;
    right: 25%;
    font-size: 1.5rem;
    color: #00d4ff;
    animation: starTwinkle 2.5s ease-in-out infinite;
    animation-delay: 1.5s;
}

/* Responsive logo adjustments */
@media (max-width: 768px) {
    .logo-title {
        gap: 15px;
    }

    .space-logo {
        font-size: clamp(2rem, 5vw, 3rem);
    }
}

@media (max-width: 480px) {
    .logo-title {
        flex-direction: column;
        gap: 10px;
    }

    .space-logo {
        font-size: 2.5rem;
    }
}

.game-title {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 900;
    background: linear-gradient(45deg, #00d4ff, #ff6b35, #fff, #00d4ff, #ff6b35);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 4s ease-in-out infinite, titleFloat 6s ease-in-out infinite, titlePulse 2s ease-in-out infinite;
    margin-bottom: 20px;
    text-shadow:
        0 0 30px rgba(0, 212, 255, 0.8),
        0 0 60px rgba(255, 107, 53, 0.6),
        0 0 90px rgba(0, 212, 255, 0.4);
    position: relative;
}

.game-title::before {
    content: 'SPACE FIGHTER';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleShine 3s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% { background-position: 0% 50%; }
    25% { background-position: 100% 50%; }
    50% { background-position: 200% 50%; }
    75% { background-position: 300% 50%; }
}

@keyframes titleFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    25% { transform: translateY(-5px) scale(1.02); }
    50% { transform: translateY(-10px) scale(1.05); }
    75% { transform: translateY(-5px) scale(1.02); }
}

@keyframes titlePulse {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.2); }
}

@keyframes titleShine {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.game-subtitle {
    font-size: clamp(1rem, 3vw, 1.5rem);
    color: #ccc;
    font-weight: 400;
}

.menu-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    align-items: center;
    animation: menuSlideIn 1s ease-out;
    position: relative;
    z-index: 10;
}

@keyframes menuSlideIn {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Buttons with Animations */
.game-btn {
    background: linear-gradient(45deg, #1a1a2e, #16213e);
    border: 2px solid #00d4ff;
    color: #fff;
    padding: 15px 30px;
    border-radius: 10px;
    cursor: pointer;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    font-size: 1rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    min-width: 200px;
    animation: buttonFloat 4s ease-in-out infinite;
}

.game-btn:nth-child(1) { animation-delay: 0s; }
.game-btn:nth-child(2) { animation-delay: 0.5s; }
.game-btn:nth-child(3) { animation-delay: 1s; }

@keyframes buttonFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.game-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.4), transparent);
    transition: left 0.6s ease;
}

.game-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
    border-radius: 50%;
}

.game-btn:hover::before {
    left: 100%;
}

.game-btn:hover::after {
    width: 300px;
    height: 300px;
}

.game-btn:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
        0 15px 35px rgba(0, 212, 255, 0.4),
        0 0 20px rgba(0, 212, 255, 0.6),
        inset 0 0 20px rgba(0, 212, 255, 0.1);
    border-color: #ff6b35;
    animation: buttonHover 0.3s ease;
}

@keyframes buttonHover {
    0% { transform: translateY(-8px) scale(1.05); }
    50% { transform: translateY(-10px) scale(1.08); }
    100% { transform: translateY(-8px) scale(1.05); }
}

.game-btn.primary {
    background: linear-gradient(45deg, #ff6b35, #e55a2b, #ff6b35);
    background-size: 200% 200%;
    border-color: #ff6b35;
    animation: buttonFloat 4s ease-in-out infinite, primaryGlow 3s ease-in-out infinite;
}

@keyframes primaryGlow {
    0%, 100% {
        background-position: 0% 50%;
        box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
    }
    50% {
        background-position: 100% 50%;
        box-shadow: 0 0 30px rgba(255, 107, 53, 0.6);
    }
}

.game-btn.primary:hover {
    box-shadow:
        0 15px 35px rgba(255, 107, 53, 0.5),
        0 0 25px rgba(255, 107, 53, 0.8),
        inset 0 0 20px rgba(255, 107, 53, 0.1);
}

.game-btn:active {
    transform: translateY(-2px) scale(0.98);
    animation: buttonPress 0.1s ease;
}

@keyframes buttonPress {
    0% { transform: translateY(-2px) scale(0.98); }
    50% { transform: translateY(0px) scale(0.95); }
    100% { transform: translateY(-2px) scale(0.98); }
}

/* Instructions Screen */
.instructions-container,
.highscore-container,
.pause-container,
.gameover-container {
    background: rgba(26, 26, 46, 0.95);
    border: 2px solid #00d4ff;
    border-radius: 15px;
    padding: 40px;
    max-width: 600px;
    width: 90%;
    text-align: center;
    backdrop-filter: blur(10px);
}

.instructions-container h2,
.highscore-container h2 {
    color: #00d4ff;
    margin-bottom: 30px;
    font-size: 2.5rem;
}

.instructions-content {
    margin-bottom: 30px;
}

.instruction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
}

.key {
    background: #ff6b35;
    color: #fff;
    padding: 8px 15px;
    border-radius: 5px;
    font-weight: 700;
    min-width: 80px;
}

.description {
    color: #ccc;
    flex: 1;
    text-align: right;
}

.game-rules {
    text-align: left;
    margin-bottom: 30px;
}

.game-rules h3 {
    color: #00d4ff;
    margin-bottom: 15px;
}

.game-rules ul {
    list-style: none;
    padding-left: 0;
}

.game-rules li {
    margin-bottom: 10px;
    color: #ccc;
    padding-left: 30px;
    position: relative;
}

.instruction-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.instruction-buttons .game-btn {
    min-width: 180px;
}

.welcome-message {
    background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(255, 107, 53, 0.1));
    border: 2px solid #00d4ff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    text-align: center;
    animation: welcomePulse 2s ease-in-out infinite;
}

.welcome-message h2 {
    color: #00d4ff;
    margin-bottom: 10px;
    font-size: 2rem;
}

.welcome-message p {
    color: #fff;
    font-size: 1.1rem;
    margin: 0;
}

@keyframes welcomePulse {
    0%, 100% {
        border-color: #00d4ff;
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    }
    50% {
        border-color: #ff6b35;
        box-shadow: 0 0 25px rgba(255, 107, 53, 0.4);
    }
}

/* Game Screen */
.game-ui {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    font-weight: 700;
    font-size: 1.1rem;
}

.ui-left, .ui-right {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.ui-center {
    display: flex;
    align-items: center;
}

.pause-btn {
    background: rgba(0, 0, 0, 0.7);
    border: 2px solid #00d4ff;
    color: #00d4ff;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.pause-btn:hover {
    background: #00d4ff;
    color: #000;
}

#gameCanvas {
    border: 2px solid #00d4ff;
    border-radius: 10px;
    background: #000;
    display: block;
    margin: 0 auto;
    max-width: 100%;
    max-height: 80vh;
}

/* Enhanced Mobile Controls */
.mobile-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: none;
    gap: 25px;
    align-items: center;
    animation: mobileControlsSlideUp 0.8s ease-out;
}

@keyframes mobileControlsSlideUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(100px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.mobile-btn {
    width: 65px;
    height: 65px;
    background: rgba(0, 212, 255, 0.9);
    border: 3px solid #00d4ff;
    border-radius: 50%;
    color: #fff;
    font-size: 1.6rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    animation: mobileBtnFloat 3s ease-in-out infinite;
    box-shadow:
        0 5px 15px rgba(0, 212, 255, 0.3),
        inset 0 0 20px rgba(0, 212, 255, 0.1);
}

.mobile-btn:nth-child(1) { animation-delay: 0s; }
.mobile-btn:nth-child(2) { animation-delay: 0.5s; }
.mobile-btn:nth-child(3) { animation-delay: 1s; }

@keyframes mobileBtnFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-3px) scale(1.02); }
}

.mobile-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.4s ease, height 0.4s ease;
}

.mobile-btn:hover,
.mobile-btn:active {
    background: #00d4ff;
    transform: translateY(-5px) scale(1.15);
    box-shadow:
        0 10px 25px rgba(0, 212, 255, 0.6),
        0 0 30px rgba(0, 212, 255, 0.8),
        inset 0 0 25px rgba(255, 255, 255, 0.2);
    animation: mobileBtnPress 0.2s ease;
}

.mobile-btn:hover::before,
.mobile-btn:active::before {
    width: 100px;
    height: 100px;
}

@keyframes mobileBtnPress {
    0% { transform: translateY(-5px) scale(1.15); }
    50% { transform: translateY(-8px) scale(1.2); }
    100% { transform: translateY(-5px) scale(1.15); }
}

.mobile-btn.shoot {
    width: 85px;
    height: 85px;
    background: rgba(255, 107, 53, 0.9);
    border-color: #ff6b35;
    font-size: 1.8rem;
    animation: shootBtnPulse 2s ease-in-out infinite, mobileBtnFloat 3s ease-in-out infinite;
    box-shadow:
        0 8px 20px rgba(255, 107, 53, 0.4),
        inset 0 0 25px rgba(255, 107, 53, 0.1);
}

@keyframes shootBtnPulse {
    0%, 100% {
        box-shadow:
            0 8px 20px rgba(255, 107, 53, 0.4),
            inset 0 0 25px rgba(255, 107, 53, 0.1);
    }
    50% {
        box-shadow:
            0 12px 30px rgba(255, 107, 53, 0.7),
            0 0 40px rgba(255, 107, 53, 0.9),
            inset 0 0 30px rgba(255, 107, 53, 0.2);
    }
}

.mobile-btn.shoot:hover,
.mobile-btn.shoot:active {
    background: #ff6b35;
    transform: translateY(-8px) scale(1.2);
    box-shadow:
        0 15px 35px rgba(255, 107, 53, 0.8),
        0 0 50px rgba(255, 107, 53, 1),
        inset 0 0 30px rgba(255, 255, 255, 0.3);
}

/* High Scores */
.score-list {
    margin-bottom: 30px;
    min-height: 200px;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    margin-bottom: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border-left: 4px solid #00d4ff;
}

.score-rank {
    color: #ff6b35;
    font-weight: 700;
    min-width: 30px;
}

.score-name {
    flex: 1;
    text-align: center;
    color: #fff;
}

.score-points {
    color: #00d4ff;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
}

/* Game Over Screen */
.gameover-title {
    color: #ff6b35;
    font-size: 3rem;
    margin-bottom: 30px;
    text-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
}

.final-stats {
    margin-bottom: 30px;
}

.stat {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
}

.stat-label {
    color: #ccc;
}

.stat-value {
    color: #00d4ff;
    font-weight: 700;
}

.new-high-score {
    background: linear-gradient(45deg, #ff6b35, #00d4ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.name-input {
    margin-bottom: 30px;
}

.name-input input {
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #00d4ff;
    border-radius: 8px;
    padding: 10px 15px;
    color: #fff;
    font-family: 'Orbitron', monospace;
    font-size: 1rem;
    margin-right: 10px;
    width: 200px;
}

.name-input input:focus {
    outline: none;
    border-color: #ff6b35;
    box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

.gameover-menu,
.pause-menu {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-controls {
        display: flex;
    }
    
    .game-ui {
        font-size: 0.9rem;
        top: 10px;
        left: 10px;
        right: 10px;
    }
    
    .ui-left, .ui-right {
        font-size: 0.8rem;
    }
    
    #gameCanvas {
        width: 100%;
        height: auto;
        max-height: 70vh;
    }
    
    .instructions-container,
    .highscore-container,
    .pause-container,
    .gameover-container {
        padding: 30px 20px;
        margin: 20px;
    }
    
    .game-title {
        font-size: 3rem;
    }
    
    .instruction-item {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .description {
        text-align: center;
    }
    
    .gameover-menu,
    .pause-menu {
        flex-direction: column;
        align-items: center;
    }
    
    .game-btn {
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    .game-title {
        font-size: 2.5rem;
    }
    
    .game-subtitle {
        font-size: 1rem;
    }
    
    .instructions-container h2,
    .highscore-container h2 {
        font-size: 2rem;
    }
    
    .mobile-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .mobile-btn.shoot {
        width: 70px;
        height: 70px;
    }
    
    .final-stats {
        font-size: 0.9rem;
    }
}

/* Enhanced Screen Transitions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.5) rotate(180deg);
    }
    to {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

.screen.active > * {
    animation: fadeIn 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.screen.active .title-container {
    animation: zoomIn 1.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.screen.active .menu-container {
    animation: slideInFromLeft 1s ease-out 0.3s both;
}

.screen.active .instructions-container,
.screen.active .highscore-container {
    animation: slideInFromRight 0.8s ease-out;
}

.screen.active .gameover-container {
    animation: zoomIn 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.screen.active .pause-container {
    animation: fadeIn 0.5s ease;
}

/* Button Press Effect */
.game-btn:active,
.mobile-btn:active,
.pause-btn:active {
    transform: scale(0.95);
}

/* Glow Effects */
.game-btn.primary {
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

.game-btn.secondary {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

/* Enhanced Game UI Styling */
.score, .level, .lives, .power {
    background: rgba(0, 0, 0, 0.8);
    padding: 8px 12px;
    border-radius: 8px;
    border: 2px solid #00d4ff;
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
    animation: uiPulse 3s ease-in-out infinite;
}

.score:hover, .level:hover, .lives:hover, .power:hover {
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
}

@keyframes uiPulse {
    0%, 100% {
        border-color: #00d4ff;
        box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
    }
    50% {
        border-color: #ff6b35;
        box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
    }
}

#score, #level, #lives {
    color: #00d4ff;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.8);
}

#power {
    color: #ff6b35;
    text-shadow: 0 0 10px rgba(255, 107, 53, 0.8);
    animation: powerGlow 2s ease-in-out infinite;
}

@keyframes powerGlow {
    0%, 100% {
        color: #ff6b35;
        text-shadow: 0 0 10px rgba(255, 107, 53, 0.8);
    }
    50% {
        color: #fff;
        text-shadow: 0 0 20px rgba(255, 107, 53, 1);
    }
}

/* Special Effects */
.screen-flash {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.3);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.1s ease;
}

.screen-flash.active {
    opacity: 1;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .stars-bg {
        animation: none;
    }
}
